<!-- 增值税月报 -->
<template>
  <div
    class="tab-content"
    v-mask="{ message: compIncomeTaxQuarterLoadingText, visible: compIncomeTaxQuarterLoading, global: true }">
    <div class="tab-content-down-title">
      <span>预缴税款信息</span>
      <img
        class="jinbio-icon"
        src="@/assets/TaxDeclaration/jinbi.png"
        alt="" />
      <span>实际应补（退）所得税额：</span>
      <span class="taxAmount">12,121,323,212</span>
    </div>
    <el-form
      :model="compIncomeTaxQuarter.quickFillModel"
      style="overflow: hidden"
      label-suffix="："
      label-position="top">
      <el-row :gutter="48">
        <el-col :span="6">
          <el-form-item label="季初从业人数">
            <el-input
              v-model="compIncomeTaxQuarter.quickFillModel.startEmployeeNum"
              @input="markAsModified"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="季初资产总额（万元）">
            <el-input
              v-model="compIncomeTaxQuarter.quickFillModel.startAssetTotal"
              @input="markAsModified"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="营业收入本年累计额（元）">
            <el-input
              v-model="compIncomeTaxQuarter.quickFillModel.currentYearIncome"
              @input="markAsModified"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="利润总额本年累计额（元）">
            <el-input
              v-model="compIncomeTaxQuarter.quickFillModel.currentYearProfit"
              @input="markAsModified"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="季末从业人数">
            <el-input
              v-model="compIncomeTaxQuarter.quickFillModel.endEmployeeNum"
              @input="markAsModified"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="季末资产总额（万元）">
            <el-input
              v-model="compIncomeTaxQuarter.quickFillModel.endAssetTotal"
              @input="markAsModified"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="营业成本本年累计额（元）">
            <el-input
              v-model="compIncomeTaxQuarter.quickFillModel.currentYearCost"
              @input="markAsModified"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="可弥补亏损额（元）">
            <el-input
              v-model="compIncomeTaxQuarter.quickFillModel.lossAmount"
              @input="markAsModified"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-dialog
      v-model="visible"
      :title="dialogTitle"
      :height="200"
      :id="dialogId"
      modal-class="modal-class"
      :close-on-click-modal="false"
      class="custom-confirm dialogDrag">
      <div
        class="dialog-main"
        v-dialogDrag>
        <div>已预填数据，若存在预填数据与实际情况填写不符 的情形，请根据实际情况自行修正报表</div>
        <br />
        <div class="monthNotMind">
          <el-checkbox
            label="本月不在提醒"
            @change="handleNotMindChange"
            v-model="monthNotMind"></el-checkbox>
        </div>
      </div>
      <template #footer>
        <a
          class="button solid-button"
          @click="visible = false">
          知道了{{ secondRemaining }}
        </a>
      </template>
    </el-dialog>

    <!-- 保存提示对话框 -->
    <el-dialog
      v-model="savePromptVisible"
      title="提示"
      width="400px"
      class="dialogDrag"
      modal-class="modal-class"
      :close-on-click-modal="false">
      <div
        class="dialog-main"
        v-dialogDrag>
        <div>是否保存居民企业（查账征收）企业所得税月（季）度申报表的填写数据？</div>
      </div>
      <template #footer>
        <div>
          <a
            class="button mr-10"
            @click="handleNotSave">
            不保存
          </a>
          <a
            class="button solid-button"
            @click="handleSave">
            保存
          </a>
        </div>
      </template>
    </el-dialog>

    <!-- 账表核对弹窗 -->
    <el-dialog
      v-model="isShowCheckDialog"
      title="账表核对"
      class="dialogDrag"
      width="900px"
      :close-on-click-modal="false">
      <div v-dialogDrag>
        <ElAlert
          type="warning"
          show-icon
          :closable="false"
          class="mb-4">
          <div>
            1、财务账是指财务模块中的对应所属期的财务报表对应项目的数据；未建账或未做账均取0，不提示；
            <br />
            2、本年累计数是指本年累计发生的数据；
          </div>
        </ElAlert>
        <LMTable
          row-key="id"
          :data="checkTableData"
          :columns="checkTableDatacolumns">
          <template #computedCell="{ slotColumn }">
            <el-table-column v-bind="slotColumn">
              <template #default="{ row }">
                <Popover
                  placement="right"
                  :content="row[slotColumn.prop].note">
                  <template #trigger>
                    <div class="column-data">
                      <div class="equal-icon" />
                      <span style="float: right; line-height: 12px">{{ row[slotColumn.prop].value }}</span>
                    </div>
                  </template>
                </Popover>
              </template>
            </el-table-column>
          </template>
        </LMTable>
      </div>
      <template #footer>
        <div>
          <a
            class="button mr-10"
            @click="closeCheckDialog">
            返回
          </a>
          <a
            class="button solid-button"
            @click="goCheckQuarter">
            去检查
          </a>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
  import { useCompIncomeTaxQuarterStore } from "@/store/modules/compIncomeTaxQuarter"
  import { getDialogNotMindLocalStorage, setDialogNotMindLocalStorage } from "../utils"
  import { ElNotify } from "@/utils/notify"
  import { storeToRefs } from "pinia"

  const compIncomeTaxQuarterStore = useCompIncomeTaxQuarterStore()
  const { compIncomeTaxQuarter, compIncomeTaxQuarterLoading, compIncomeTaxQuarterLoadingText } = storeToRefs(compIncomeTaxQuarterStore)
  const { saveCompIncomeTaxQuarter } = compIncomeTaxQuarterStore

  const visible = ref(false)
  const dialogTitle = "提示"
  const secondRemaining = ref(0)
  const dialogId = "CompIncomeTaxQuarterDialog"

  // 数据是否被修改的状态
  const isDataModified = ref(false)
  // 保存提示对话框状态
  const savePromptVisible = ref(false)
  // 是否显示账表核对弹窗
  const isShowCheckDialog = ref(false)
  // 待执行的回调函数
  const pendingCallback = ref<Function | null>(null)

  // 数据被修改时调用此函数
  const markAsModified = () => {
    isDataModified.value = true
  }

  // 保存数据
  const save = () => {
    // 这里实现保存逻辑，可能需要调用store中的方法
    // compIncomeTaxQuarterStore.save()
    console.log("保存数据")

    return saveCompIncomeTaxQuarter()
      .then(() => {
        ElNotify({
          type: "success",
          message: "数据保存成功",
        })
        isDataModified.value = false // 保存后重置修改状态
      })
      .catch((error) => {
        console.error("数据保存失败", error)
      })
  }

  // 检查数据是否被修改，如果被修改则显示提示对话框，否则直接执行回调
  function checkDataModified(callback: Function): boolean {
    if (isDataModified.value) {
      savePromptVisible.value = true
      pendingCallback.value = callback
      return true // 数据已修改，正在等待用户确认
    } else {
      // 数据未修改，直接执行回调
      callback()
      return false
    }
  }

  // 点击"保存"按钮
  async function handleSave() {
    savePromptVisible.value = false // 关闭对话框
    await save() // 保存数据

    // 执行待处理的回调函数
    if (pendingCallback.value) {
      pendingCallback.value()
      pendingCallback.value = null
    }
  }

  // 点击"不保存"按钮
  function handleNotSave() {
    savePromptVisible.value = false // 关闭对话框
    isDataModified.value = false // 重置修改状态

    // 执行待处理的回调函数
    if (pendingCallback.value) {
      pendingCallback.value()
      pendingCallback.value = null
    }
  }

  // 账表核对弹窗
  const checkTableDatacolumns = [
    {
      label: "核对指标",
      prop: "checkIndex",
    },
    {
      label: "本期数",
      children: [
        {
          label: "申报表",
          prop: "declarationForm",
          slot: "computedCell",
        },
        {
          label: "财务账",
          prop: "financialAccount",
          slot: "computedCell",
        },
      ],
    },
    {
      label: "本年累计",
      children: [
        {
          label: "申报表",
          prop: "declarationFormYear",
          slot: "computedCell",
        },
        {
          label: "财务账",
          prop: "financialAccountYear",
          slot: "computedCell",
        },
      ],
    },
    {
      label: "核对结果",
      prop: "checkResult",
    },
    {
      label: "可能原因",
      prop: "possibleReason",
    },
  ]

  const checkTableData = [
    {
      checkIndex: "营业收入",
      declarationForm: {
        value: "1000000",
        note: "dwadwa",
      },
      financialAccount: {
        value: "1020000",
        note: "dw1adwa",
      },
      declarationFormYear: {
        value: "********",
        note: "dwadwa",
      },
      financialAccountYear: {
        value: "********",
        note: "dw1adwa",
      },
      checkResult: "一致",
      possibleReason: "",
    },
    {
      checkIndex: "营业成本",
      declarationForm: {
        value: "2000000",
        note: "dwadwa",
      },
      financialAccount: {
        value: "2020000",
        note: "dw1adwa",
      },
      declarationFormYear: {
        value: "********",
        note: "dwadwa",
      },
      financialAccountYear: {
        value: "********",
        note: "dw1adwa",
      },
      checkResult: "一致",
      possibleReason: "",
    },
  ]

  const showCheckDialog = () => {
    isShowCheckDialog.value = true
  }

  const closeCheckDialog = () => {
    isShowCheckDialog.value = false
  }
  const goCheckQuarter = () => {
    //TODO 跳转财务账套利润表对应季报，没有就跳最新的利润表季报
  }

  // 本月不在提醒弹窗
  watch(
    () => compIncomeTaxQuarterLoading.value,
    (newVal) => {
      if (!newVal) {
        let res = getDialogNotMindLocalStorage(dialogId)
        console.log(res, dialogId)
        if (res) {
          visible.value = false
          return
        } else {
          visible.value = true
          secondRemaining.value = 8
          let timer = setInterval(() => {
            if (secondRemaining.value > 0) {
              secondRemaining.value--
            } else {
              clearInterval(timer)
              visible.value = false
            }
          }, 1000)
        }
      } else {
        visible.value = false
      }
    },
  )

  const monthNotMind = ref(false)

  const handleNotMindChange = (val: boolean) => {
    if (val) {
      setDialogNotMindLocalStorage(dialogId)
    }
  }
  // 需要导出以下方法和状态，供父组件使用
  defineExpose({
    checkDataModified,
    save,
    showCheckDialog,
  } as const)
</script>
<style lang="scss" scoped>
  @use "@/style/TaxDeclaration/index.scss" as *;
</style>
