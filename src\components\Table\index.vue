<template>
  <div
    class="table custom-table"
    :class="[
      {
        'paging-show': pageIsShow,
        // 'paging-hide': !pageIsShow,
      },
    ]"
    v-loading="loading">
    <el-table
      ref="TableComponents"
      :size="size"
      :cell-class-name="cellClassName"
      :show-header="showHeader"
      :border="border"
      :data="data"
      :fit="fit"
      :stripe="stripe"
      :max-height="maxHeight"
      :height="height"
      :row-key="rowKey"
      :expand-row-keys="expandRowKeys"
      :empty-text="customEmptyText"
      :highlight-current-row="highlightCurrentRow"
      :show-overflow-tooltip="showOverflowTooltip"
      :tooltip-options="tooltipOptions"
      :cell-style="cellStyle"
      :header-cell-style="headerCellStyle"
      :scrollbar-always-on="scrollbarAlwaysOn"
      @select="handleSelect"
      @selection-change="handleSelectionChange"
      @select-all="handleSelectAll"
      @cell-click="handleCellClick"
      @row-click="handleRowClick"
      @sort-change="handleSortChange"
      @cell-mouse-enter="handleCellMouseEnter"
      @cell-mouse-leave="handleCellMouseLeave"
      :span-method="spanMethod"
      :style="{ minHeight: minHeight ? minHeight : '0px' }">
      <template
        v-for="(colItem, index) in columns"
        :key="colItem.prop">
        <ColumnItem
          :ref="(el: any) => bindColumnRef(el, index)"
          :column="colItem">
          <template
            v-for="slotItem in slotsArr"
            #[slotItem]="{ slotColumn }"
            :key="slotItem">
            <slot
              :name="slotItem"
              :slotColumn="slotColumn"></slot>
          </template>
          <template #selection="{ slotColumn }">
            <el-table-column
              type="selection"
              :width="slotColumn.width ?? 40"
              :header-align="slotColumn.headerAlign ?? 'center'"
              :align="slotColumn.align ?? 'center'"
              :selectable="selectable"
              class-name="selection-col"
              :resizable="false"
              :fixed="slotColumn.fixed ?? false"></el-table-column>
          </template>
        </ColumnItem>
      </template>
      <template #append>
        <slot name="append"></slot>
      </template>
      <template #empty>
        <slot name="empty"></slot>
      </template>
    </el-table>
    <TablePagination
      v-if="pageIsShow"
      class="pagination"
      :size="size"
      :page-sizes="pageSizes"
      :page-size="pageSize"
      :layout="layout"
      :total="total"
      :current-page="currentPage"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @refresh="handleRefresh">
      <template #pageOther>
        <slot name="pageOther"></slot>
      </template>
    </TablePagination>
  </div>
</template>
<script lang="ts" setup>
  import type { ITableProps, TableEmits } from "./types.ts"
  import ColumnItem from "./ColumnItem.vue"
  import TablePagination from "./TablePagination.vue"

  const props = withDefaults(defineProps<ITableProps>(), {
    loading: false,
    data: () => [], // Lmtips:不排除有null的情况,未做兼容处理
    columns: () => [],
    pageIsShow: false,
    border: true,
    height: "auto",
    stripe: true,
    pageSizes: () => [20, 50, 100, 200, 300],
    pageSize: 20,
    currentPage: 1,
    total: 0,
    fit: true,
    size: "default",
    rowKey: "", // 必填属性
    expandRowKeys: () => [],
    showOverflowTooltip: true,
    tooltipOptions: () => ({ effect: "light", placement: "right-start", offset: -10 }),
    showHeader: true,
    headerCellStyle: () => ({}),
    emptyText: "",
    highlightCurrentRow: true,
    selectable: (row: any, index: number): boolean => {
      return true
    },
  })

  const customEmptyText = computed(() => {
    return props.emptyText.trim() || ""
  })

  const emit = defineEmits<TableEmits>()
  const handleSelect = (selection: any, row: any) => {
    emit("select", selection, row)
  }
  const selectData = ref<Array<any>>([])
  const handleSelectionChange = (val: any) => {
    selectData.value = val
    emit("selection-change", val)
  }
  const handleSelectAll = (val: any) => {
    emit("select-all", val)
  }
  const handleCellClick = (row: any, column: any, cell: any, event: any) => {
    emit("cell-click", row, column, cell, event)
  }
  const handleRowClick = (row: any, column: any, event: any) => {
    emit("row-click", row, column, event)
  }
  const handleSortChange = (column: any, prop: any, order: any) => {
    emit("sort-change", column, prop, order)
  }
  const handleSizeChange = (val: any) => {
    emit("size-change", val)
  }
  const handleCurrentChange = (val: any) => {
    emit("current-change", val)
  }
  const handleRefresh = () => {
    emit("refresh")
  }
  const handleCellMouseEnter = (row: any, column: any, cell: HTMLTableCellElement, event: Event) => {
    emit("cell-mouse-enter", row, column, cell, event)
  }
  const handleCellMouseLeave = (row: any, column: any, cell: HTMLTableCellElement, event: Event) => {
    emit("cell-mouse-leave", row, column, cell, event)
  }

  const columnItemRefList = ref<Array<InstanceType<typeof ColumnItem> | null>>([])
  function bindColumnRef(el: any, index: number): InstanceType<typeof ColumnItem> | null {
    if (el) {
      columnItemRefList.value[index] = el
    }
    return columnItemRefList.value[index]
  }

  const slots = useSlots()
  const TableComponents = ref<any>()
  const slotsArr = Object.keys(slots)
  defineExpose({
    TableComponents,
    selectData: selectData.value,
  })
</script>
<style lang="scss" scoped>
  .table {
    position: relative;
    //后续看是否表格撑开页面高度
    // height: 100%;
    display: flex;
    flex-direction: column;

    .pagination {
      // padding-top: 10px;
      text-align: right;
    }

    :deep(.el-table-column--selection) {
      text-align: center;
    }

    :deep(.el-table) {
      overflow: visible;
      flex: 1;
      &.el-table--border::before {
        top: 0;
      }
      &.el-table--border::after {
        top: 0;
      }
      .el-popper.is-light,
      .el-popper.is-dark {
        max-width: 300px;
        text-align: left;
      }
      .cell {
        .el-select {
          // width: 100%;
        }
      }
      & .cell.el-tooltip {
        min-width: 0px;
        line-height: 20px;
      }
      .el-table__header,
      .el-table__body {
        .el-table__row {
          &.row-selected {
            background-color: var(--table-selected-color);
          }
        }
        .el-table-column--selection.el-table__cell {
          .cell {
            padding: 0 4px;
            min-width: 30px;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
      }
      &.el-table--enable-row-hover .el-table__body tr.row-selected:hover > td.el-table__cell {
        background-color: var(--table-selected-hover-color);
      }
      .el-table__header {
        .el-table__cell {
          &.show_overflow_tooltip {
            .cell {
              white-space: nowrap;
            }
          }
        }
      }
      .selection-col {
        .cell {
          display: flex;
          align-items: center;
        }
        .el-checkbox {
          height: unset;
        }
      }
    }

    &.paging-show {
      :deep(.el-table) {
        display: flex;
        flex-direction: column;
        .el-table__inner-wrapper {
          flex: 1;
          &::before {
            height: 0;
          }
        }
        .el-table__body-wrapper {
          padding-bottom: 14px;
        }
        .el-scrollbar {
          position: static;
        }
      }
    }

    // &.paging-hide {
    //   :deep(.el-table) {
    //     // border-bottom: 1px solid var(--el-border-color-lighter);
    //   }
    // }
  }
</style>
