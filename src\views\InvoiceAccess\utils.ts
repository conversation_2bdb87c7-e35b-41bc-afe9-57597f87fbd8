// 格式化份数显示
export const formatCount = (count: number | string) => {
  if (count === undefined || count === null || count === "") {
    return "0" // 份数没值显示0
  }
  return count
}

// 格式化金额显示
export const formatValue = (value: string | number) => {
  // 其他项目处理
  if (value === undefined || value === null || value === "") {
    return "0.00" // 无值显示0.00
  }

  if (Number(value) === 0) {
    return "0.00"
  }

  // 格式化金额
  return Number(value).toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })
}

// 根据月份返回对应的季度起始日
export const getQuarterStartDate = (month: number, year?: number): string => {
  // 如果没有传入年份，使用当前年份
  const currentYear = year || new Date().getFullYear()

  // 根据月份确定季度起始月份
  let quarterStartMonth: number

  if (month >= 1 && month <= 3) {
    // 第一季度：1-3月，起始日为1月1日
    quarterStartMonth = 1
  } else if (month >= 4 && month <= 6) {
    // 第二季度：4-6月，起始日为4月1日
    quarterStartMonth = 4
  } else if (month >= 7 && month <= 9) {
    // 第三季度：7-9月，起始日为7月1日
    quarterStartMonth = 7
  } else if (month >= 10 && month <= 12) {
    // 第四季度：10-12月，起始日为10月1日
    quarterStartMonth = 10
  } else {
    throw new Error("月份必须在1-12之间")
  }

  // 格式化为 YYYY-MM-DD 格式
  const formattedMonth = quarterStartMonth.toString().padStart(2, "0")
  return `${currentYear}-${formattedMonth}-01`
}

// 根据月份返回对应的季度结束日
export const getQuarterEndDate = (month: number, year?: number): string => {
  // 如果没有传入年份，使用当前年份
  const currentYear = year || new Date().getFullYear()

  // 根据月份确定季度结束月份和日期
  let quarterEndMonth: number
  let quarterEndDay: number

  if (month >= 1 && month <= 3) {
    // 第一季度：1-3月，结束日为3月31日
    quarterEndMonth = 3
    quarterEndDay = 31
  } else if (month >= 4 && month <= 6) {
    // 第二季度：4-6月，结束日为6月30日
    quarterEndMonth = 6
    quarterEndDay = 30
  } else if (month >= 7 && month <= 9) {
    // 第三季度：7-9月，结束日为9月30日
    quarterEndMonth = 9
    quarterEndDay = 30
  } else if (month >= 10 && month <= 12) {
    // 第四季度：10-12月，结束日为12月31日
    quarterEndMonth = 12
    quarterEndDay = 31
  } else {
    throw new Error("月份必须在1-12之间")
  }

  // 格式化为 YYYY-MM-DD 格式
  const formattedMonth = quarterEndMonth.toString().padStart(2, "0")
  const formattedDay = quarterEndDay.toString().padStart(2, "0")
  return `${currentYear}-${formattedMonth}-${formattedDay}`
}

// 根据月份获取季度信息
export const getQuarterInfo = (month: number, year?: number) => {
  const currentYear = year || new Date().getFullYear()

  let quarter: number
  if (month >= 1 && month <= 3) {
    quarter = 1
  } else if (month >= 4 && month <= 6) {
    quarter = 2
  } else if (month >= 7 && month <= 9) {
    quarter = 3
  } else if (month >= 10 && month <= 12) {
    quarter = 4
  } else {
    throw new Error("月份必须在1-12之间")
  }

  return {
    year: currentYear,
    quarter,
    startDate: getQuarterStartDate(month, year),
    endDate: getQuarterEndDate(month, year),
    label: `${currentYear}年第${quarter}季度`,
  }
}

// 根据日期字符串获取季度起始日（支持 YYYY-MM 或 YYYY-MM-DD 格式）
export const getQuarterStartDateFromString = (dateString: string): string => {
  const date = new Date(dateString)
  const year = date.getFullYear()
  const month = date.getMonth() + 1 // getMonth() 返回 0-11，需要 +1

  return getQuarterStartDate(month, year)
}

// 根据日期字符串获取季度结束日（支持 YYYY-MM 或 YYYY-MM-DD 格式）
export const getQuarterEndDateFromString = (dateString: string): string => {
  const date = new Date(dateString)
  const year = date.getFullYear()
  const month = date.getMonth() + 1 // getMonth() 返回 0-11，需要 +1

  return getQuarterEndDate(month, year)
}
