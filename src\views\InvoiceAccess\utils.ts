// 格式化份数显示
export const formatCount = (count: number | string) => {
  if (count === undefined || count === null || count === "") {
    return "0" // 份数没值显示0
  }
  return count
}

// 格式化金额显示
export const formatValue = (value: string | number) => {
  // 其他项目处理
  if (value === undefined || value === null || value === "") {
    return "0.00" // 无值显示0.00
  }

  if (Number(value) === 0) {
    return "0.00"
  }

  // 格式化金额
  return Number(value).toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })
}
