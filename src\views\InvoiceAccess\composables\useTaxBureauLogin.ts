import { ref, onUnmounted, watch } from "vue"
import { ElNotify } from "@/utils/notify"
import { IResponseModel, Jrequest } from "@/utils/service"

export interface IInvoiceTaskResult {
  asId: number
  startDate: number
  endDate: number
  createdBy: number
  createdDate: string
  modifiedDate: string
  status: number
  remark: string
  invoiceTaskStatus: number
  invoiceTaskRemark: string
  purchaseInvalidTotal: number
  purchaseInvalidTax: number
  purchaseInvalidAmount: number
  purchaseInvalidCount: number
  purchaseNormalTotal: number
  purchaseNormalTax: number
  purchaseNormalAmount: number
  purchaseNormalCount: number
  purchaseRedTotal: number
  purchaseRedTax: number
  purchaseRedAmount: number
  purchaseRedCount: number
  salesInvalidTotal: number
  salesInvalidTax: number
  salesInvalidAmount: number
  salesInvalidCount: number
  salesNormalTotal: number
  salesNormalTax: number
  salesNormalAmount: number
  salesNormalCount: number
  salesRedTotal: number
  salesRedTax: number
  salesRedAmount: number
  salesRedCount: number

  deductionTaskStatus: number
  deductionTaskRemark: string
  deductionCount: number
  deductionAmount: number

  fileTaskStatus: number
  fileTaskRemark: string
  fileCount: number

  createdByName: string
  taxLoginType: number
}

export const useTaxBureauLogin = () => {
  const taskStartLoading = ref(false)
  const hasInvoiceTask = ref(false)
  const timer = ref<number | null>(null)
  const taskCompleted = ref(false)
  const invoiceTaskList = ref<any[]>([]) // Updated to use ref with type any[]
  // 监听任务状态变化
  watch(hasInvoiceTask, (newVal, oldVal) => {
    // 当任务从有变为无时，表示任务完成
    if (oldVal && !newVal) {
      taskCompleted.value = true
    }
  })

  const getInvoiceTaskList = async (preCheck = false) => {
    if (!preCheck) taskStartLoading.value = true

    await Jrequest({
      url: `/api/TaxBureau/LastSummaryTask`,
    }).then((res: IResponseModel<Array<IInvoiceTaskResult>>) => {
      invoiceTaskList.value = res.data
      hasInvoiceTask.value = invoiceTaskList.value && invoiceTaskList.value[0] && invoiceTaskList.value[0].status === 1
      if (hasInvoiceTask.value) {
        reInvoiceTaskList()
      } else {
        if (!preCheck) {
          ElNotify({
            type: "success",
            message: "取票成功,自动刷新列表数据和页面按钮",
          })
        }
        unInvoiceTaskList()
      }
    })
  }

  const reInvoiceTaskList = () => {
    if (timer.value) return
    timer.value = window.setInterval(() => {
      getInvoiceTaskList()
    }, 8 * 1000)
  }

  const unInvoiceTaskList = () => {
    if (timer.value) {
      clearInterval(timer.value)
      timer.value = null
    }
    taskStartLoading.value = false
  }

  onUnmounted(() => {
    unInvoiceTaskList()
  })

  return {
    taskStartLoading,
    hasInvoiceTask,
    taskCompleted,
    getInvoiceTaskList,
    invoiceTaskList,
  }
}
