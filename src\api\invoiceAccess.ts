import { IResponseModel, requestWithPeriodId } from "@/utils/service"
import { IDeductionItem, IInputInvoiceItem, IOutputInvoiceItem } from "@/views/InvoiceAccess/types"

export interface IInvoiceTaskValue {
  success: boolean
  result: string
  message: string
  info: string
  taskId: number
  reportId: number
}

export interface ICodeItem {
  item1: string
  item2: string
}

export interface IResSalesInvoice {
  list: IOutputInvoiceItem[]
  codes: ICodeItem[]
}

// get /api/invoice/purchase-list
// 进项发票分页列表
export async function getPurchaseList(): Promise<IResponseModel<IInputInvoiceItem[]>> {
  return await requestWithPeriodId({
    url: "api/invoice/purchase-list",
    method: "get",
  })
}
// get /api/invoice/sales-list
// 销项发票分页列表

export async function getSalesList(): Promise<IResponseModel<IResSalesInvoice>> {
  return await requestWithPeriodId({
    url: "api/invoice/sales-list",
    method: "get",
  })
}
// get /api/invoice/summary-check
// 生成申报表是否可用
export async function getSummaryCheck(): Promise<IResponseModel<boolean>> {
  return await requestWithPeriodId({
    url: "api/invoice/summary-check",
    method: "get",
  })
}

// get /api/invoice/purchase-summary
// 进项统计表
export function getPurchaseSummary(): Promise<IResponseModel<Array<IDeductionItem>>> {
  return requestWithPeriodId({
    url: "api/invoice/purchase-summary",
    method: "get",
  })
}
// get /api/invoice/invoice-table-check
// 获取票表核对信息
export function getInvoiceTableCheck(): Promise<IResponseModel<any>> {
  return requestWithPeriodId({
    url: "api/invoice/invoice-table-check",
    method: "get",
  })
}
