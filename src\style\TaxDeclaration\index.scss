@use "@/style/functions.scss" as *;

@mixin equal-icon($selector) {
  &:hover {
    #{$selector} {
      .equal-icon {
        float: left;
        width: 12px;
        height: 12px;
        background: url("@/assets/Icons/equal.png") no-repeat 0 0;
        background-size: 12px 12px;
      }
    }
  }
}
.el-table {
  overflow: visible !important;
  .el-table__header-wrapper {
    z-index: 3;
    position: sticky;
    border-top: 1px var(--el-border-color) var(--el-border-style);
    top: 0;
  }

  .el-table__row {
    @include equal-icon(".cell");
  }
}
// 创建一个mixin函数用于计算colspan的宽度百分比
@mixin colspan-width($total-columns: 10) {
  .native-table {
    table {
      @for $i from 1 through $total-columns {
        th[colspan="#{$i}"],
        td[colspan="#{$i}"] {
          width: calc($i / $total-columns) * 100%;
        }
      }
      // 针对没有设置colspan的单元格，设置默认宽度
      th:not([colspan]),
      td:not([colspan]) {
        width: calc(100% / #{$total-columns});
      }
    }
  }
}
.native-table {
  table {
    width: 100%;
    border-collapse: collapse;
    border: 1px var(--el-border-color) var(--el-border-style);

    tr {
      &.table-data:nth-child(odd) {
        background: var(--table-striped-color);
      }
      &.table-data:hover {
        background: var(--table-hover-color);
      }
      &.table-data {
        color: red;
        @include equal-icon(td);
      }
    }

    th,
    td {
      box-sizing: border-box;
      padding: 8px;
      height: var(--table-body-height);
      border: 1px var(--el-border-color) var(--el-border-style);
      color: var(--font-color);
      font-size: var(--table-title-font-size);
      line-height: var(--table-title-line-height);
    }

    th {
      background: var(--table-title-color);
      font-weight: bold;
    }
    td {
      padding: 0 8px;
      min-width: 0;
      position: relative;
      text-overflow: ellipsis;
      vertical-align: middle;
      z-index: var(--el-table-index);

      &.align-right {
        text-align: right;
      }
    }
  }
}

.tab-content {
  font-size: var(--h4);
  color: var(--dark-grey);
  padding: 0 10px;
  .tab-content-down-title {
    margin: 15px 0;
    &::before {
      @include common-before;
    }
    line-height: 20px;

    span {
      margin-left: 4px;
    }

    img {
      width: 16px;
      height: 16px;
      vertical-align: sub;
      margin-left: 30px;
    }

    .taxAmount {
      color: rgba(250, 100, 0, 1);
    }
  }
}
.dialog-main {
  flex-direction: column;
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
// 表格相关样式
.mb-4 {
  margin-bottom: 16px;
}

.declaration-form {
  .form-title {
    margin: 17px;
    font-size: var(--h3);
    text-align: center;
  }

  .form-subtitle {
    margin-bottom: 14px;
    font-weight: 400;
    font-size: var(--h4);
    color: var(--dark-grey);
    text-align: center;
  }

  .form-info {
    margin-bottom: 12px;

    .info {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      div {
        box-sizing: border-box;

        span {
          line-height: 20px;
          font-size: var(--h4);
        }
      }
    }
    span {
      flex: 1;
      align-self: flex-end;
      line-height: 32px;
      font-size: 14px;
      text-align: right;
    }
  }
}

.main-table {
  .title {
    display: inline-block;
    padding: 8px 16px;
    margin-bottom: 15px;
    border: 1px solid var(--button-border-color);
  }

  .el-divider--horizontal {
    margin: 20px 0;
  }
}
