import { IInvoiceTypeListItem } from "./types"

export enum EnumTabName {
  销项发票 = "outputInvoice",
  进项发票 = "inputInvoice",
  进项统计表 = "inputStatistics",
}

// 若要变动枚举值，搜一下使用地方
export const invoiceTypeListAll: Array<IInvoiceTypeListItem> = [
  {
    IN_ID: "1070",
    IN_NAME: "全电发票（增值税专用发票）",
  },
  {
    IN_ID: "1060",
    IN_NAME: "全电发票（普通发票）",
  },
  {
    IN_ID: "1010",
    IN_NAME: "未开具发票",
  },
  {
    IN_ID: "1020",
    IN_NAME: "增值税普通发票",
  },
  {
    IN_ID: "1030",
    IN_NAME: "增值税专用发票",
  },
  {
    IN_ID: "1110",
    IN_NAME: "机动车销售统一发票",
  },
  {
    IN_ID: "1090",
    IN_NAME: "二手车销售统一发票",
  },
  {
    IN_ID: "1040",
    IN_NAME: "税控机动车专用发票",
  },
  {
    IN_ID: "1050",
    IN_NAME: "纳税检查调整",
  },
]
export const invoiceTypeListAllPur: Array<IInvoiceTypeListItem> = [
  {
    IN_ID: "1070",
    IN_NAME: "全电发票（增值税专用发票）",
  },
  {
    IN_ID: "1060",
    IN_NAME: "全电发票（普通发票）",
  },
  {
    IN_ID: "1030",
    IN_NAME: "增值税专用发票",
  },
  {
    IN_ID: "1020",
    IN_NAME: "增值税普通发票",
  },
  {
    IN_ID: "1110",
    IN_NAME: "机动车销售统一发票",
  },
  {
    IN_ID: "1090",
    IN_NAME: "二手车销售统一发票",
  },
  {
    IN_ID: "1040",
    IN_NAME: "税控机动车专用发票",
  },
]

export enum EnumTaxpayerLevel {
  /// <summary>
  /// 小规模纳税人
  /// </summary>
  Small = 1,
  /// <summary>
  /// 一般纳税人
  /// </summary>
  Normal = 2,
}
